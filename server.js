const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const path = require('path');
const { db, initializeDatabase } = require('./database');

const app = express();
const PORT = process.env.PORT || 3000;
const JWT_SECRET = process.env.JWT_SECRET || 'saude_flex_secret_key_2024';

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname)));

// Middleware de autenticação
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Token de acesso requerido' });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Token inválido' });
        }
        req.user = user;
        next();
    });
}

// Middleware para verificar permissões
function checkPermission(requiredTypes) {
    return (req, res, next) => {
        if (!requiredTypes.includes(req.user.tipo)) {
            return res.status(403).json({ error: 'Permissão insuficiente' });
        }
        next();
    };
}

// Inicializar banco de dados
initializeDatabase();

// Rotas para servir páginas HTML
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'login.html'));
});

// Rota para o sistema principal
app.get('/sistema', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// API Routes

// Autenticação
app.post('/api/auth/login', (req, res) => {
    const { email, senha } = req.body;

    if (!email || !senha) {
        return res.status(400).json({ error: 'Email e senha são obrigatórios' });
    }

    db.get('SELECT * FROM usuarios WHERE email = ? AND status = "ativo"', [email], (err, user) => {
        if (err) {
            console.error('Erro na consulta:', err);
            return res.status(500).json({ error: 'Erro interno do servidor' });
        }

        if (!user) {
            return res.status(401).json({ error: 'Credenciais inválidas' });
        }

        // Verificar senha
        bcrypt.compare(senha, user.senha, (err, isValid) => {
            if (err) {
                console.error('Erro ao verificar senha:', err);
                return res.status(500).json({ error: 'Erro interno do servidor' });
            }

            if (!isValid) {
                return res.status(401).json({ error: 'Credenciais inválidas' });
            }

            // Atualizar último login
            db.run('UPDATE usuarios SET ultimo_login = CURRENT_TIMESTAMP WHERE id = ?', [user.id]);

            // Gerar token JWT
            const token = jwt.sign(
                {
                    id: user.id,
                    email: user.email,
                    tipo: user.tipo,
                    nome: user.nome
                },
                JWT_SECRET,
                { expiresIn: '24h' }
            );

            // Remover senha da resposta
            const { senha: _, ...userWithoutPassword } = user;

            res.json({
                message: 'Login realizado com sucesso',
                token,
                user: userWithoutPassword
            });
        });
    });
});

app.post('/api/auth/logout', authenticateToken, (req, res) => {
    // Em uma implementação real, você poderia invalidar o token
    res.json({ message: 'Logout realizado com sucesso' });
});

app.get('/api/auth/me', authenticateToken, (req, res) => {
    db.get('SELECT id, nome, email, tipo, status, ultimo_login FROM usuarios WHERE id = ?', [req.user.id], (err, user) => {
        if (err) {
            return res.status(500).json({ error: 'Erro interno do servidor' });
        }

        if (!user) {
            return res.status(404).json({ error: 'Usuário não encontrado' });
        }

        res.json(user);
    });
});

// Usuários (apenas admins)
app.get('/api/usuarios', authenticateToken, checkPermission(['admin']), (req, res) => {
    db.all('SELECT id, nome, email, tipo, status, ultimo_login, data_cadastro FROM usuarios ORDER BY nome', (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

app.post('/api/usuarios', authenticateToken, checkPermission(['admin']), (req, res) => {
    const { nome, email, senha, tipo, status = 'ativo' } = req.body;

    if (!nome || !email || !senha || !tipo) {
        return res.status(400).json({ error: 'Todos os campos obrigatórios devem ser preenchidos' });
    }

    // Criptografar senha
    bcrypt.hash(senha, 10, (err, hashedPassword) => {
        if (err) {
            return res.status(500).json({ error: 'Erro ao criptografar senha' });
        }

        const sql = `INSERT INTO usuarios (nome, email, senha, tipo, status) VALUES (?, ?, ?, ?, ?)`;
        db.run(sql, [nome, email, hashedPassword, tipo, status], function(err) {
            if (err) {
                if (err.message.includes('UNIQUE constraint failed')) {
                    return res.status(400).json({ error: 'Email já está em uso' });
                }
                return res.status(500).json({ error: err.message });
            }
            res.json({ id: this.lastID, message: 'Usuário criado com sucesso!' });
        });
    });
});

app.put('/api/usuarios/:id', authenticateToken, checkPermission(['admin']), (req, res) => {
    const { id } = req.params;
    const { nome, email, tipo, status, senha } = req.body;

    if (!nome || !email || !tipo) {
        return res.status(400).json({ error: 'Nome, email e tipo são obrigatórios' });
    }

    if (senha) {
        // Se senha foi fornecida, criptografar e atualizar
        bcrypt.hash(senha, 10, (err, hashedPassword) => {
            if (err) {
                return res.status(500).json({ error: 'Erro ao criptografar senha' });
            }

            const sql = `UPDATE usuarios SET nome = ?, email = ?, senha = ?, tipo = ?, status = ?, data_atualizacao = CURRENT_TIMESTAMP WHERE id = ?`;
            db.run(sql, [nome, email, hashedPassword, tipo, status, id], function(err) {
                if (err) {
                    if (err.message.includes('UNIQUE constraint failed')) {
                        return res.status(400).json({ error: 'Email já está em uso' });
                    }
                    return res.status(500).json({ error: err.message });
                }
                res.json({ message: 'Usuário atualizado com sucesso!' });
            });
        });
    } else {
        // Atualizar sem senha
        const sql = `UPDATE usuarios SET nome = ?, email = ?, tipo = ?, status = ?, data_atualizacao = CURRENT_TIMESTAMP WHERE id = ?`;
        db.run(sql, [nome, email, tipo, status, id], function(err) {
            if (err) {
                if (err.message.includes('UNIQUE constraint failed')) {
                    return res.status(400).json({ error: 'Email já está em uso' });
                }
                return res.status(500).json({ error: err.message });
            }
            res.json({ message: 'Usuário atualizado com sucesso!' });
        });
    }
});

app.delete('/api/usuarios/:id', authenticateToken, checkPermission(['admin']), (req, res) => {
    const { id } = req.params;

    // Não permitir excluir o próprio usuário
    if (parseInt(id) === req.user.id) {
        return res.status(400).json({ error: 'Não é possível excluir seu próprio usuário' });
    }

    db.run('DELETE FROM usuarios WHERE id = ?', [id], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ message: 'Usuário excluído com sucesso!' });
    });
});

// Clientes
app.get('/api/clientes', (req, res) => {
    db.all('SELECT * FROM clientes WHERE status = "ativo" ORDER BY nome', (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

app.post('/api/clientes', (req, res) => {
    const { nome, email, telefone, endereco, cidade, estado, cep, data_nascimento, genero, observacoes } = req.body;
    
    const sql = `INSERT INTO clientes (nome, email, telefone, endereco, cidade, estado, cep, data_nascimento, genero, observacoes) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    
    db.run(sql, [nome, email, telefone, endereco, cidade, estado, cep, data_nascimento, genero, observacoes], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ id: this.lastID, message: 'Cliente cadastrado com sucesso!' });
    });
});

app.put('/api/clientes/:id', (req, res) => {
    const { id } = req.params;
    const { nome, email, telefone, endereco, cidade, estado, cep, data_nascimento, genero, observacoes } = req.body;
    
    const sql = `UPDATE clientes SET nome = ?, email = ?, telefone = ?, endereco = ?, cidade = ?, estado = ?, 
                 cep = ?, data_nascimento = ?, genero = ?, observacoes = ?, data_atualizacao = CURRENT_TIMESTAMP 
                 WHERE id = ?`;
    
    db.run(sql, [nome, email, telefone, endereco, cidade, estado, cep, data_nascimento, genero, observacoes, id], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ message: 'Cliente atualizado com sucesso!' });
    });
});

// Produtos
app.get('/api/produtos', (req, res) => {
    db.all('SELECT * FROM produtos WHERE status = "ativo" ORDER BY categoria, nome', (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

app.post('/api/produtos', (req, res) => {
    const { nome, categoria, descricao, preco, duracao_visita } = req.body;
    
    const sql = `INSERT INTO produtos (nome, categoria, descricao, preco, duracao_visita) VALUES (?, ?, ?, ?, ?)`;
    
    db.run(sql, [nome, categoria, descricao, preco, duracao_visita], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ id: this.lastID, message: 'Produto cadastrado com sucesso!' });
    });
});

// Funcionários
app.get('/api/funcionarios', (req, res) => {
    db.all('SELECT * FROM funcionarios WHERE status = "ativo" ORDER BY nome', (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

// Agendamentos
app.get('/api/agendamentos', (req, res) => {
    const sql = `
        SELECT a.*, c.nome as cliente_nome, f.nome as funcionario_nome, p.nome as produto_nome
        FROM agendamentos a
        LEFT JOIN clientes c ON a.cliente_id = c.id
        LEFT JOIN funcionarios f ON a.funcionario_id = f.id
        LEFT JOIN produtos p ON a.produto_id = p.id
        ORDER BY a.data_visita, a.hora_inicio
    `;
    
    db.all(sql, (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

app.post('/api/agendamentos', (req, res) => {
    const { cliente_id, funcionario_id, produto_id, data_visita, hora_inicio, hora_fim, endereco_visita, observacoes, valor } = req.body;
    
    const sql = `INSERT INTO agendamentos (cliente_id, funcionario_id, produto_id, data_visita, hora_inicio, hora_fim, endereco_visita, observacoes, valor) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    
    db.run(sql, [cliente_id, funcionario_id, produto_id, data_visita, hora_inicio, hora_fim, endereco_visita, observacoes, valor], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ id: this.lastID, message: 'Agendamento criado com sucesso!' });
    });
});

app.put('/api/agendamentos/:id', (req, res) => {
    const { id } = req.params;
    const { cliente_id, funcionario_id, produto_id, data_visita, hora_inicio, hora_fim, endereco_visita, observacoes, valor, status } = req.body;

    const sql = `UPDATE agendamentos SET cliente_id = ?, funcionario_id = ?, produto_id = ?, data_visita = ?,
                 hora_inicio = ?, hora_fim = ?, endereco_visita = ?, observacoes = ?, valor = ?, status = ?,
                 data_atualizacao = CURRENT_TIMESTAMP WHERE id = ?`;

    db.run(sql, [cliente_id, funcionario_id, produto_id, data_visita, hora_inicio, hora_fim, endereco_visita, observacoes, valor, status, id], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ message: 'Agendamento atualizado com sucesso!' });
    });
});

app.put('/api/agendamentos/:id/status', (req, res) => {
    const { id } = req.params;
    const { status } = req.body;

    db.run('UPDATE agendamentos SET status = ?, data_atualizacao = CURRENT_TIMESTAMP WHERE id = ?', [status, id], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ message: 'Status atualizado com sucesso!' });
    });
});

app.delete('/api/agendamentos/:id', (req, res) => {
    const { id } = req.params;

    db.run('DELETE FROM agendamentos WHERE id = ?', [id], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ message: 'Agendamento excluído com sucesso!' });
    });
});

// Dashboard - Estatísticas
app.get('/api/dashboard/stats', (req, res) => {
    const stats = {};
    
    // Total de clientes ativos
    db.get('SELECT COUNT(*) as total FROM clientes WHERE status = "ativo"', (err, row) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        stats.totalClientes = row.total;
        
        // Agendamentos do mês atual
        db.get(`SELECT COUNT(*) as total FROM agendamentos 
                WHERE strftime('%Y-%m', data_visita) = strftime('%Y-%m', 'now')`, (err, row) => {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            stats.agendamentosMes = row.total;
            
            // Receita do mês
            db.get(`SELECT SUM(valor) as total FROM agendamentos 
                    WHERE strftime('%Y-%m', data_visita) = strftime('%Y-%m', 'now') 
                    AND status IN ('concluido', 'confirmado')`, (err, row) => {
                if (err) {
                    res.status(500).json({ error: err.message });
                    return;
                }
                stats.receitaMes = row.total || 0;
                
                // Agendamentos hoje
                db.get(`SELECT COUNT(*) as total FROM agendamentos 
                        WHERE date(data_visita) = date('now')`, (err, row) => {
                    if (err) {
                        res.status(500).json({ error: err.message });
                        return;
                    }
                    stats.agendamentosHoje = row.total;
                    res.json(stats);
                });
            });
        });
    });
});

// Iniciar servidor
app.listen(PORT, () => {
    console.log(`Servidor rodando na porta ${PORT}`);
    console.log(`Acesse: http://localhost:${PORT}`);
});
