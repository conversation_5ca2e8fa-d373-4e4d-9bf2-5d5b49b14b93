{"name": "saude-flex-agendamentos", "version": "1.0.0", "description": "Sistema avançado de agendamentos para empresa de saúde e bem-estar", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node database.js"}, "keywords": ["agendamentos", "saude", "bem-estar", "visitas"], "author": "<PERSON><PERSON> Flex", "license": "MIT", "dependencies": {"bcryptjs": "^3.0.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.0.1"}}